import { useState, useEffect } from "react";
import { Eye, EyeOff, Mail, Lock, AlertCircle } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import LoadingSpinner from "../components/common/LoadingSpinner";
import { displayBackendStatus } from "../utils/backendChecker";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import { loginUser, clearError, setBelongsToCompany } from "../store/slices/authSlice";

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, error, isAuthenticated, belongsToCompany } = useAppSelector((state) => state.auth);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && belongsToCompany) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, navigate]);

  // Test backend on component mount
  useEffect(() => {
    displayBackendStatus();
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Form submitted, preventing default');

    // Clear any previous errors
    dispatch(clearError());

    try {
      console.log('Attempting login with:', { email: formData.email });

      // Dispatch login action
      const result = await dispatch(loginUser({
        email: formData.email,
        password: formData.password,
      }));

      console.log('Login result:', result);

      // If login is successful, redirect to dashboard
      if (loginUser.fulfilled.match(result)) {
        if (belongsToCompany) {
          navigate("/dashboard")
        } else {
          navigate("/onboarding/company")
        }
      }
      // Error handling is done by Redux automatically
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  const handleGoogleLogin = () => {
    console.log("Google login clicked");
    // Add your Google login logic here
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background relative overflow-hidden">
      {/* Background elements for glassmorphism effect */}
      <div className="absolute w-72 h-72 rounded-full bg-soft-purple opacity-5 blur-3xl -top-20 -left-20"></div>
      <div className="absolute w-96 h-96 rounded-full bg-soft-teal opacity-5 blur-3xl -bottom-20 -right-20"></div>
      <div className="absolute w-64 h-64 rounded-full bg-soft-blue opacity-5 blur-3xl top-1/2 left-1/4"></div>

      <div className="glass-card p-8 w-full max-w-md z-10">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-semibold text-white mb-2">
            Welcome Back
          </h1>
          <p className="text-text-secondary text-sm">
            Sign in to your TeamCheck account
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6" noValidate>
          <div className="space-y-4">
            {/* Email Input */}
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-white mb-1"
              >
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail size={16} className="text-soft-blue opacity-70" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="input-dark block w-full pl-10 pr-3 py-2.5 rounded-lg text-white text-sm"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            {/* Password Input */}
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-white mb-1"
              >
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock size={16} className="text-soft-purple opacity-70" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="input-dark block w-full pl-10 pr-10 py-2.5 rounded-lg text-white text-sm"
                  placeholder="••••••••"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-soft-purple opacity-70 hover:opacity-100 focus:outline-none"
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  name="rememberMe"
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={handleChange}
                  className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                />
                <label
                  htmlFor="rememberMe"
                  className="ml-2 block text-sm text-text-secondary"
                >
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <Link
                  to="/forgot-password"
                  className="text-soft-blue hover:text-white transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-500 bg-opacity-10 border border-red-500 text-red-500 px-4 py-3 rounded-lg flex items-center mb-4">
              <AlertCircle size={16} className="mr-2" />
              <span className="text-white">{error}</span>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className="w-full py-2.5 px-4 btn-white font-medium rounded-lg text-sm flex items-center justify-center"
          >
            {loading ? (
              <>
                <LoadingSpinner size={20} color="#4F46E5" className="mr-2" />
                Signing In...
              </>
            ) : (
              "Sign In"
            )}
          </button>

          {/* Divider */}
          <div className="relative flex items-center justify-center mt-6">
            <div className="border-t border-border w-full opacity-30"></div>
            <div className="absolute px-4 text-xs text-text-secondary bg-surface-2 backdrop-blur-sm">
              or continue with
            </div>
          </div>

          {/* Google Sign In Button */}
          <button
            type="button"
            onClick={handleGoogleLogin}
            className="w-full flex items-center justify-center py-2.5 px-4 btn-white-ghost text-sm rounded-lg"
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
              <path fill="none" d="M1 1h22v22H1z" />
            </svg>
            Sign in with Google
          </button>

          {/* Sign Up Link */}
          <div className="text-center mt-4">
            <p className="text-text-secondary">
              Don't have an account?{" "}
              <Link
                to="/signup"
                className="text-soft-blue hover:text-white font-medium transition-colors"
              >
                Sign up
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
